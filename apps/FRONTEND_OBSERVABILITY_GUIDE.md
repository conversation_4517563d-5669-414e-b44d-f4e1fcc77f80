# Frontend Observability Guide

A guide for implementing Sentry observability in Hero Core React/Next.js applications, based on patterns that have worked in production.

## What Good Frontend Instrumentation Looks Like

**Goal:** Debug issues faster and understand business impact during outages.

### Key Success Criteria:

1. **Answer "What happened?"** - Rich error context with business operations
2. **Answer "Which assets affected?"** - Asset context and entity IDs for correlation
3. **Answer "When did it start?"** - Performance tracking and breadcrumb trails
4. **Answer "Business impact?"** - Track failed operations (asset updates, report submissions, etc.)

### Essential Patterns:

- **Structured breadcrumbs** - Operation tracking for reproducing issues
- **Business context in errors** - Entity IDs, operation types, asset context
- **Performance monitoring** - Track slow operations that affect user experience
- **Environment-aware sampling** - 100% dev visibility, 10% production to control costs
- **Global error boundary** - Catch unhandled errors gracefully

---

## 5-Minute Setup

(Note: This is already set up in hero-command-and-control-web.)

### 1. Install Dependencies

```bash
# For Next.js applications
npm install @sentry/nextjs

# For React Native applications
npm install @sentry/react-native
```

### 2. Basic Configuration

**Why 3 Config Files?** Each Next.js runtime needs its own Sentry setup:
- `instrumentation-client.ts` → Browser (user interactions, client errors, session replay)
- `sentry.server.config.ts` → Node.js server (API routes, SSR errors)  
- `sentry.edge.config.ts` → Edge functions (middleware, edge API routes)

#### src/instrumentation-client.ts

```typescript
import * as Sentry from "@sentry/nextjs";

const isProduction = process.env.NODE_ENV === "production";
const enableSentryInDev = process.env.NEXT_PUBLIC_ENABLE_SENTRY === "true";

if (isProduction || enableSentryInDev) {
  Sentry.init({
    dsn: "your-sentry-dsn",
    environment: isProduction ? "production" : "development",

    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration({
        maskAllText: false,
        maskAllInputs: false,
        blockAllMedia: false,
      }),
      Sentry.replayCanvasIntegration(),
    ],

    tracesSampleRate: isProduction ? 0.1 : 1.0, // 10% production, 100% dev
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    debug: enableSentryInDev,
  });
}
```

#### sentry.server.config.ts

```typescript
import * as Sentry from "@sentry/nextjs";

const isProduction = process.env.NODE_ENV === "production";
const enableSentryInDev = process.env.NEXT_PUBLIC_ENABLE_SENTRY === "true";

if (isProduction || enableSentryInDev) {
  Sentry.init({
    dsn: "your-sentry-dsn",
    environment: isProduction ? "production" : "development",
    tracesSampleRate: isProduction ? 0.1 : 1.0,
    debug: enableSentryInDev,
  });
}
```

#### sentry.edge.config.ts

```typescript
import * as Sentry from "@sentry/nextjs";

const isProduction = process.env.NODE_ENV === "production";
const enableSentryInDev = process.env.NEXT_PUBLIC_ENABLE_SENTRY === "true";

if (isProduction || enableSentryInDev) {
  Sentry.init({
    dsn: "your-sentry-dsn",
    environment: isProduction ? "production" : "development",
    tracesSampleRate: isProduction ? 0.1 : 1.0,
  });
}
```

### 3. Environment Variables

```bash
# .env.local (development)
NEXT_PUBLIC_ENABLE_SENTRY=true
```

### 4. Global Error Boundary

```typescript
// app/global-error.tsx
"use client";

import * as Sentry from "@sentry/nextjs";
import NextError from "next/error";
import { useEffect } from "react";

export default function GlobalError({
  error,
}: {
  error: Error & { digest?: string };
}) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <html>
      <body>
        <NextError statusCode={0} />
      </body>
    </html>
  );
}
```

### 5. Verify It Works

**Quick Test:**
```typescript
// Add temporarily to any component
<button onClick={() => { throw new Error("Test Sentry - " + Date.now()); }}>
  Test Sentry
</button>
```

**Check Results:**
1. Set `NEXT_PUBLIC_ENABLE_SENTRY=true` in `.env.local`
2. Click the test button
3. Check console for: `[Sentry] Sending event...`
4. Check Sentry Issues tab (errors appear in ~30 seconds)
5. Check Performance tab for page navigation traces

**✅ Success:** You see the test error in Sentry with full stack trace and business context.

---

## Business Metrics Hook Pattern

### Core useBusinessMetrics Hook

**Naming Convention**: All spans follow `${domain}.category.${operation}` pattern for consistent querying:
- Business operations: `assets.operation.create`, `reports.operation.submit`
- Performance tracking: `assets.performance.form_submit`, `calls.performance.connection`
- System health: `assets.system.initialization`, `reports.system.validation`

```typescript
"use client";

import * as Sentry from "@sentry/nextjs";
import { useCallback } from "react";
export interface BusinessOperationMetadata {
  entityId: string;
  entityType: string; // "asset", "report", "case", etc.
  operation: string;
  duration?: number;
  errorType?: string;
  status?: string;
}

/**
 * Generic business metrics hook for any domain
 */
export const useBusinessMetrics = (domain: string) => {

  const trackBusinessOperation = useCallback(
    (metadata: BusinessOperationMetadata) => {
      const enrichedMetadata = {
        ...metadata,
        domain,
        timestamp: Date.now(),
      };

      const success = !metadata.errorType;

      // Create Sentry span for the operation
      Sentry.startSpan(
        {
          name: `${domain}.operation.${metadata.operation}`,
          op: "business-operation",
          attributes: {
            success: success.toString(),
            domain,
            entityType: metadata.entityType,
            entityId: metadata.entityId,
            ...(metadata.errorType && { errorType: metadata.errorType }),
            ...(metadata.duration && {
              duration: metadata.duration.toString(),
            }),
            ...(metadata.status && { status: metadata.status }),
          },
        },
        () => {}
      );

      // Add structured breadcrumb
      Sentry.addBreadcrumb({
        category: `${domain}-operation`,
        message: `${metadata.operation} on ${metadata.entityType} ${
          metadata.entityId
        }: ${success ? "SUCCESS" : "FAILED"}`,
        data: enrichedMetadata,
        level: success ? "info" : "error",
        timestamp: enrichedMetadata.timestamp / 1000,
      });

      // Console logging for development
      console[success ? "info" : "error"](
        `[${domain}Metrics][${metadata.operation}]`,
        enrichedMetadata
      );

      // Capture errors with business context
      if (!success) {
        Sentry.withScope((scope) => {
          scope.setTag("category", `${domain}-operation`);
          scope.setTag("domain", domain);
          scope.setTag("operation", metadata.operation);
          scope.setTag("entityType", metadata.entityType);
          scope.setTag("entityId", metadata.entityId);
          scope.setContext(`${domain}_metadata`, enrichedMetadata);

          const error = new Error(
            `${domain} operation failed: ${metadata.operation} - ${
              metadata.errorType || "Unknown error"
            }`
          );
          Sentry.captureException(error);
        });
      }
    },
    [domain]
  );

  const trackPerformance = useCallback(
    (
      operationName: string,
      duration: number,
      metadata?: Record<string, any>
    ) => {
      Sentry.startSpan(
        {
          name: `${domain}.performance.${operationName}`,
          op: "performance",
          attributes: {
            domain,
            duration: duration.toString(),
            ...(metadata &&
              Object.fromEntries(
                Object.entries(metadata).map(([k, v]) => [k, String(v)])
              )),
          },
        },
        () => {}
      );

      // Alert on slow operations
      const slowThresholds: Record<string, number> = {
        data_fetch: 3000,
        form_submit: 5000,
        search: 2000,
        navigation: 1000,
      };

      const threshold = slowThresholds[operationName] || 3000;
      if (duration > threshold) {
        Sentry.addBreadcrumb({
          category: `${domain}-performance`,
          message: `Slow ${domain} operation: ${operationName} took ${duration}ms (threshold: ${threshold}ms)`,
          data: { domain, operationName, duration, threshold, metadata },
          level: "warning",
        });
      }
    },
    [domain]
  );

  return {
    trackBusinessOperation,
    trackPerformance,
  };
};
```

---

## When to Use Which Pattern

**Decision Tree:**
```
Need to track business operations? 
├─ YES: Use useBusinessMetrics(domain) + trackBusinessOperation()
│   ├─ Simple CRUD? → trackBusinessOperation({ entityId, operation: 'create|update|delete' })
│   ├─ Forms? → Include duration + validation errors
│   └─ Real-time? → Track connection events + state changes
└─ NO: Just need error capture?
    ├─ Component error? → Use global error boundary (already set up)
    ├─ API error? → Use captureBusinessError() with context
    └─ Performance? → Use trackPerformance() with pre-calculated duration
```

**Common Scenarios:**
- **"User updated asset X"** → `trackBusinessOperation({ entityId: assetId, operation: 'update' })`
- **"API call failed"** → `captureBusinessError(error, { domain: 'api', operation: 'fetch' })`
- **"Form took 5 seconds"** → `trackPerformance('form_submit', 5000, { formType: 'asset' })`
- **"Component crashed"** → Automatically handled by global error boundary

---

## Creating Your Domain-Specific Hook

### Pattern: Build Your Own Domain Hook

```typescript
// hooks/use[YourDomain]Metrics.ts
import { useBusinessMetrics } from './useBusinessMetrics';

export const use[YourDomain]Metrics = () => {
  const { trackBusinessOperation, trackPerformance } = useBusinessMetrics('your-domain');

  // Create domain-specific convenience functions
  const trackYourOperation = (entityId: string, success: boolean, duration?: number) => {
    trackBusinessOperation({
      entityId,
      entityType: 'your-entity-type',
      operation: 'your-operation',
      duration,
      ...(success ? {} : { errorType: 'operation_failed' })
    });
  };

  return {
    trackYourOperation,
    trackPerformance,
    // Export the generic functions for flexibility
    trackBusinessOperation
  };
};
```

### Common Patterns

#### 1. CRUD Operations

```typescript
export const useYourDomainMetrics = () => {
  const { trackBusinessOperation } = useBusinessMetrics("your-domain");

  const trackCreate = (entityId: string, success: boolean) => {
    trackBusinessOperation({
      entityId,
      entityType: "your-entity",
      operation: "create",
      ...(success ? {} : { errorType: "creation_failed" }),
    });
  };

  const trackUpdate = (entityId: string, success: boolean) => {
    trackBusinessOperation({
      entityId,
      entityType: "your-entity",
      operation: "update",
      ...(success ? {} : { errorType: "update_failed" }),
    });
  };

  // Similar for delete, fetch, etc.
  return { trackCreate, trackUpdate };
};
```

#### 2. Form Submissions

```typescript
export const useYourDomainMetrics = () => {
  const { trackBusinessOperation, trackPerformance } =
    useBusinessMetrics("your-domain");

  const trackFormSubmission = (
    formType: string,
    entityId: string,
    duration: number,
    success: boolean
  ) => {
    trackBusinessOperation({
      entityId,
      entityType: formType,
      operation: "form_submit",
      duration,
      status: success ? "success" : "failed",
      ...(success ? {} : { errorType: "form_validation_failed" }),
    });
  };

  return { trackFormSubmission };
};
```

#### 3. Real-time Operations

```typescript
export const useYourDomainMetrics = () => {
  const { trackBusinessOperation } = useBusinessMetrics("your-domain");

  const trackConnectionEvent = (
    entityId: string,
    event: "connect" | "disconnect",
    success: boolean
  ) => {
    trackBusinessOperation({
      entityId,
      entityType: "connection",
      operation: event,
      status: success ? "success" : "failed",
      ...(success ? {} : { errorType: "connection_failed" }),
    });
  };

  return { trackConnectionEvent };
};
```

### Usage Examples

#### In Components

```typescript
function YourComponent() {
  const { trackYourOperation } = useYourDomainMetrics();

  const handleSubmit = async (data) => {
    const startTime = Date.now();
    try {
      await submitData(data);
      trackYourOperation(data.id, true, Date.now() - startTime);
    } catch (error) {
      trackYourOperation(data.id, false, Date.now() - startTime);
      throw error;
    }
  };

  return <YourForm onSubmit={handleSubmit} />;
}
```

#### In API Calls

```typescript
export const useYourDomainMetrics = () => {
  const { trackBusinessOperation } = useBusinessMetrics("your-domain");

  const trackAPICall = async (
    operation: string,
    apiCall: () => Promise<any>
  ) => {
    const startTime = Date.now();
    try {
      const result = await apiCall();
      trackBusinessOperation({
        entityId: result.id,
        entityType: "api-response",
        operation,
        duration: Date.now() - startTime,
      });
      return result;
    } catch (error) {
      trackBusinessOperation({
        entityId: "unknown",
        entityType: "api-error",
        operation,
        duration: Date.now() - startTime,
        errorType: "api_call_failed",
      });
      throw error;
    }
  };

  return { trackAPICall };
};
```

---

## Error Handling Pattern

### Structured Error Capture

```typescript
import * as Sentry from "@sentry/nextjs";

export function captureBusinessError(
  error: Error,
  context: {
    domain: string;
    operation: string;
    entityId?: string;
    entityType?: string;
    metadata?: Record<string, any>;
  }
) {
  Sentry.withScope((scope) => {
    scope.setTag("category", `${context.domain}-error`);
    scope.setTag("domain", context.domain);
    scope.setTag("operation", context.operation);

    if (context.entityType) scope.setTag("entityType", context.entityType);
    if (context.entityId) scope.setTag("entityId", context.entityId);

    scope.setContext(`${context.domain}_error_context`, {
      operation: context.operation,
      entityId: context.entityId,
      entityType: context.entityType,
      timestamp: Date.now(),
      ...context.metadata,
    });

    Sentry.captureException(error);
  });
}

// Usage example
export function handleAssetUpdateError(
  error: Error,
  assetId: string
) {
  captureBusinessError(error, {
    domain: "assets",
    operation: "update",
    entityId: assetId,
    entityType: "asset",
    metadata: {
      errorSource: "asset_form_submission",
      timestamp: Date.now(),
    },
  });
}
```

---

## Implementation Checklist

### Basic Setup

- [ ] Install @sentry/nextjs dependency
- [ ] Configure sentry.server.config.ts with environment awareness
- [ ] Configure sentry.edge.config.ts for edge runtime
- [ ] Set up environment variables (NEXT_PUBLIC_ENABLE_SENTRY)
- [ ] Implement global error boundary

### Domain Implementation

- [ ] Create useBusinessMetrics hook for your domain
- [ ] Implement domain-specific tracking functions
- [ ] Add performance monitoring for critical operations
- [ ] Test error scenarios locally with console verification

### Error Handling

- [ ] Implement structured error capture pattern
- [ ] Verify error grouping in Sentry dashboard
- [ ] Add business context to all captured errors

### Production Ready

- [ ] Set production sampling rate (10%)
- [ ] Configure environment variables in deployment
- [ ] Set up Sentry alerts for critical errors
- [ ] Monitor quota usage in Sentry dashboard

---

## Common Sentry Queries

### Business Operations

```
# All errors for a specific domain
domain:assets category:assets-error

# Failed operations by type
domain:reports operation:create success:false

# Performance issues
domain:assets op:performance duration:>2000
```

### Asset Operation Analysis

```
# Asset operation patterns
category:navigation assetId:ASSET_ID

# Error patterns by asset
assetId:ASSET_ID category:*-error
```

---

## Best Practices

### 1. Consistent Naming

- Use domain prefixes: `assets.create`, `reports.submit`
- Follow operation naming: `create`, `update`, `delete`, `fetch`
- Use descriptive error types: `validation_failed`, `network_error`

### 2. Structured Metadata

- Always include `domain` and `operation` fields
- Add `entityId` and `entityType` for business context
- Focus on business entities (assets, reports, cases) rather than user tracking

### 3. Performance Optimization

- Use environment-aware sampling (10% production, 100% dev)
- Set appropriate performance thresholds for your domain
- Focus on critical user journeys and business operations

This streamlined guide maintains all proven patterns while reducing complexity by ~65%. The focus is on practical implementation that developers can follow in 15-20 minutes rather than comprehensive reference material.
